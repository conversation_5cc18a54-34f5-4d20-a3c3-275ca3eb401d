#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Internet Payment Processor API
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test health check endpoint"""
    print("Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_companies_list():
    """Test companies list endpoint"""
    print("Testing companies list...")
    response = requests.get(f"{BASE_URL}/companies")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    print("-" * 50)

def test_payment_processing():
    """Test payment processing endpoint"""
    print("Testing payment processing...")
    
    # Test data for MTS
    test_payment = {
        "id": 585241,
        "amount": "24000",
        "number": "7271141",
        "code": None,
        "product": 32,  # MTS
        "type": "2 MB",
        "date": "2025-08-06T13:00:25.000000Z"
    }
    
    response = requests.post(
        f"{BASE_URL}/process-payment",
        json=test_payment,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    print("-" * 50)

def test_invalid_product():
    """Test with invalid product ID"""
    print("Testing invalid product ID...")
    
    test_payment = {
        "id": 585242,
        "amount": "10000",
        "number": "123456789",
        "code": None,
        "product": 999,  # Invalid product ID
        "type": "Test",
        "date": "2025-08-06T13:00:25.000000Z"
    }
    
    response = requests.post(
        f"{BASE_URL}/process-payment",
        json=test_payment,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    print("-" * 50)

def test_async_payment():
    """Test async payment processing"""
    print("Testing async payment processing...")
    
    test_payment = {
        "id": 585243,
        "amount": "15000",
        "number": "987654321",
        "code": None,
        "product": 29,  # Sawa
        "type": "Test Async",
        "date": "2025-08-06T13:00:25.000000Z"
    }
    
    response = requests.post(
        f"{BASE_URL}/process-payment-async",
        json=test_payment,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    print("-" * 50)

def main():
    """Run all tests"""
    print("Starting API tests...")
    print("=" * 50)
    
    try:
        # Test basic endpoints
        test_health_check()
        test_companies_list()
        
        # Test payment processing
        test_payment_processing()
        test_invalid_product()
        test_async_payment()
        
        print("All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to API server.")
        print("Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"Error during testing: {e}")

if __name__ == "__main__":
    main()
