#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple server runner for testing
"""

import uvicorn
import os
import sys

if __name__ == "__main__":
    # Create directories if they don't exist
    os.makedirs("processors", exist_ok=True)
    os.makedirs("logs", exist_ok=True)

    print("Starting Internet Payment Processor API...")
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")

    try:
        # Import the app first to check for errors
        from main import app
        print("FastAPI app imported successfully")

        # Start the server
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info"
        )
    except Exception as e:
        print(f"Error starting server: {e}")
        import traceback
        traceback.print_exc()
