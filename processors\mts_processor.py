#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MTS Payment Processor
Handles payment processing for MTS Syria
"""

import requests
import asyncio
from typing import Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup
import logging
from .base_processor import BasePaymentProcessor

class MTSProcessor(BasePaymentProcessor):
    """MTS payment processor implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.login_url = config.get('login_url', 'https://pos.ispcare.mts.sy/')
        self.search_url = config.get('search_url', 'https://pos.ispcare.mts.sy/')
        self.payment_url = config.get('payment_url', 'https://pos.ispcare.mts.sy/')
        self.dashboard_url = config.get('dashboard_url', 'https://pos.ispcare.mts.sy/')
        self.login_data_template = config.get('login_data', {})
        
    def format_phone_number(self, phone_number: str) -> str:
        """Format phone number for MTS (add 41 prefix if needed)"""
        if not phone_number.startswith('41'):
            return f"41{phone_number}"
        return phone_number
    
    async def login(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """
        Login to MTS system
        Returns:
            Tuple[bool, Optional[Session], Optional[str]]: (success, session, error_message)
        """
        try:
            self.logger.info("Attempting to login to MTS system...")
            
            # Create new session
            session = requests.Session()
            
            # Login credentials from config
            username = self.credentials.get('username', 'alfager.net')
            password = self.credentials.get('password', 'Fager@123')

            # Build login data from template
            login_data = self.login_data_template.copy()
            login_data.update({
                'username': username,
                'password': password
            })
            
            # Set headers
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'ar,en;q=0.9',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Host': 'pos.ispcare.mts.sy',
                'Origin': 'https://pos.ispcare.mts.sy',
                'Referer': 'https://pos.ispcare.mts.sy/',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            # Get login page first
            try:
                get_response = session.get(self.login_url, headers=headers, verify=False)
                self.logger.info(f"Login page response status: {get_response.status_code}")
            except Exception as e:
                self.logger.warning(f"Could not get login page: {e}")
            
            # Send login request
            response = session.post(
                self.login_url,
                data=login_data,
                headers=headers,
                verify=False,
                allow_redirects=True
            )
            
            self.logger.info(f"Login response status: {response.status_code}")
            self.logger.info(f"Login response URL: {response.url}")
            
            # Check if login was successful
            if response.status_code == 200:
                # Check for success indicators in response
                if "logout" in response.text.lower() or "خروج" in response.text:
                    self.logger.info("MTS login successful")
                    return True, session, None
                else:
                    self.logger.error("MTS login failed - no success indicators found")
                    return False, None, "فشل في تسجيل الدخول إلى نظام MTS"
            else:
                self.logger.error(f"MTS login failed with status: {response.status_code}")
                return False, None, f"فشل في تسجيل الدخول - رمز الخطأ: {response.status_code}"
                
        except Exception as e:
            self.logger.error(f"Error during MTS login: {e}")
            return False, None, f"خطأ في تسجيل الدخول: {str(e)}"
    
    async def search_subscriber(self, session: requests.Session, phone_number: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """
        Search for subscriber in MTS system
        Args:
            session: Authenticated session
            phone_number: Phone number to search
        Returns:
            Tuple[bool, Optional[Dict], Optional[str]]: (success, subscriber_info, error_message)
        """
        try:
            self.logger.info(f"Searching for phone number: {phone_number}")
            
            # Format phone number for MTS
            formatted_number = self.format_phone_number(phone_number)
            
            # Search request data
            search_data = {
                'action': 'search',
                'phone': formatted_number
            }
            
            # Send search request
            response = session.post(
                self.search_url,
                data=search_data,
                verify=False
            )
            
            if response.status_code == 200:
                # Parse response to extract subscriber info
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for subscriber information in the response
                # This is a simplified implementation - you would need to adapt based on actual MTS response format
                if "subscriber found" in response.text.lower() or "مشترك" in response.text:
                    subscriber_info = {
                        'phone': formatted_number,
                        'found': True,
                        'details': 'Subscriber found'
                    }
                    self.logger.info(f"Subscriber found for phone: {formatted_number}")
                    return True, subscriber_info, None
                else:
                    self.logger.warning(f"Subscriber not found for phone: {formatted_number}")
                    return False, None, "المشترك غير موجود"
            else:
                self.logger.error(f"Search request failed with status: {response.status_code}")
                return False, None, f"فشل في البحث - رمز الخطأ: {response.status_code}"
                
        except Exception as e:
            self.logger.error(f"Error during subscriber search: {e}")
            return False, None, f"خطأ في البحث: {str(e)}"
    
    async def process_payment(self, order_id: int, phone_number: str, amount: str, order_type: str) -> Dict[str, Any]:
        """
        Process payment for MTS
        Args:
            order_id: Order ID
            phone_number: Customer phone number
            amount: Payment amount
            order_type: Type of order
        Returns:
            Dict[str, Any]: Payment result
        """
        self.logger.info(f"Processing MTS payment - Order: {order_id}, Phone: {phone_number}, Amount: {amount}")
        
        try:
            # Check amount limits
            is_valid, error_msg = self.check_amount_limit(amount)
            if not is_valid:
                return {
                    'success': False,
                    'message': error_msg,
                    'status': 3,  # Payment failed
                    'details': {'error': 'Amount limit exceeded'}
                }
            
            # Format phone number
            formatted_phone = self.format_phone_number(phone_number)
            
            # Check for duplicate payments
            if self.check_duplicate_payment(formatted_phone, amount):
                return {
                    'success': False,
                    'message': 'تم العثور على دفعة مماثلة خلال آخر 5 دقائق',
                    'status': 3,  # Payment failed
                    'details': {'error': 'Duplicate payment'}
                }
            
            # Step 1: Login
            login_success, session, login_error = await self.login()
            if not login_success:
                return {
                    'success': False,
                    'message': login_error or 'فشل في تسجيل الدخول',
                    'status': 3,  # Payment failed
                    'details': {'error': 'Login failed'}
                }
            
            # Step 2: Search subscriber
            search_success, subscriber_info, search_error = await self.search_subscriber(session, formatted_phone)
            if not search_success:
                return {
                    'success': False,
                    'message': search_error or 'المشترك غير موجود',
                    'status': 2,  # Number not found
                    'details': {'error': 'Subscriber not found'}
                }
            
            # Step 3: Process payment (simplified implementation)
            # In a real implementation, you would make the actual payment request here
            self.logger.info(f"Payment processed successfully for order {order_id}")
            
            return {
                'success': True,
                'message': 'تم الدفع بنجاح',
                'status': 1,  # Paid successfully
                'details': {
                    'order_id': order_id,
                    'phone': formatted_phone,
                    'amount': amount,
                    'company': 'MTS'
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error processing MTS payment: {e}")
            return {
                'success': False,
                'message': f'خطأ في معالجة الطلب: {str(e)}',
                'status': 3,  # Payment failed
                'details': {'error': str(e)}
            }
