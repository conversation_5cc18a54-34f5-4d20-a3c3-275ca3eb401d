{"companies": {"mts": {"product_id": 32, "name": "MTS Syria", "login_url": "https://pos.ispcare.mts.sy/", "search_url": "https://pos.ispcare.mts.sy/", "payment_url": "https://pos.ispcare.mts.sy/", "dashboard_url": "https://pos.ispcare.mts.sy/", "credentials": {"username": "alfager.net", "password": "Fager@123"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "41", "required": true}, "login_data": {"action": "login", "login": "إرسال"}}, "sawa": {"product_id": 29, "name": "Sawa Syria", "login_url": "http://sp.sawaisp.sy/charge_balance_to_customer", "search_url": "http://sp.sawaisp.sy/", "payment_url": "http://sp.sawaisp.sy/", "dashboard_url": "http://sp.sawaisp.sy/", "credentials": {"username": "fireware", "password": "idhmhgauvhx"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 1440}, "phone_format": {"prefix": "", "required": false}, "login_method": "selenium"}, "syriatel": {"product_id": 25, "name": "Syriatel Syria", "login_url": "https://abili.syriatel.com.sy/Login.aspx", "search_url": "https://abili.syriatel.com.sy/", "payment_url": "https://abili.syriatel.com.sy/", "dashboard_url": "https://abili.syriatel.com.sy/", "credentials": {"username": "WSLRETCOS310", "password": "Ea@456NN"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}, "login_method": "selenium", "login_fields": {"username_field": "UsernameTextBox", "password_field": "PasswordTextBox", "submit_button": "SubmitButton"}}, "inet": {"product_id": 47, "name": "INET Syria", "login_url": "https://pos.ispcare.inet.sy/", "search_url": "https://pos.ispcare.inet.sy/", "payment_url": "https://pos.ispcare.inet.sy/", "dashboard_url": "https://pos.ispcare.inet.sy/", "credentials": {"username": "naw<PERSON><PERSON><PERSON>", "password": "Idhm*0hgauvhx"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}, "login_data": {"action": "login", "login": "إرسال"}}, "linet": {"product_id": 46, "name": "Linet Syria", "login_url": "https://pos.ispcare.linet-sy.com/", "search_url": "https://pos.ispcare.linet-sy.com/", "payment_url": "https://pos.ispcare.linet-sy.com/", "dashboard_url": "https://pos.ispcare.linet-sy.com/", "credentials": {"username": "nawras-tel", "password": "Nn@456nn"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}, "login_data": {"action": "login", "lang": "ar", "login": "إرسال"}}, "takamol": {"product_id": 45, "name": "Takamol Syria", "login_url": "https://pos.ispcare.takamol.sy/", "search_url": "https://pos.ispcare.takamol.sy/", "payment_url": "https://pos.ispcare.takamol.sy/", "dashboard_url": "https://pos.ispcare.takamol.sy/", "credentials": {"username": "firewire", "password": "Idhm*0hgauvhx"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}, "login_data": {"action": "login", "lang": "ar", "login": "إرسال"}}, "lema": {"product_id": 148, "name": "LEMA Syria", "login_url": "https://pos.ispcare.lema.sy/", "search_url": "https://pos.ispcare.lema.sy/", "payment_url": "https://pos.ispcare.lema.sy/", "dashboard_url": "https://pos.ispcare.lema.sy/", "credentials": {"username": "ahmad-s", "password": "Meme.meme07"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}, "login_data": {"action": "login", "login": "إرسال"}}, "bitakat": {"product_id": 50, "name": "Bitakat Syria", "login_url": "https://pos.ispcare.bitakat.sy/", "search_url": "https://pos.ispcare.bitakat.sy/", "payment_url": "https://pos.ispcare.bitakat.sy/", "dashboard_url": "https://pos.ispcare.bitakat.sy/", "credentials": {"username": "naoras-tech", "password": "Idhm*9hgauvhx"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}, "login_data": {"action": "login", "login": "إرسال"}}}, "api_settings": {"retry_attempts": 3, "log_level": "INFO", "verify_ssl": false, "session_timeout": 300}, "external_api": {"orders_url": "https://menfax.com/pos/public/api/orders", "status_url": "https://menfax.com/pos/public/api/change-order-status", "secret": "SECRET1265AQREFGHKLFS!@#"}}