#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Syriatel Payment Processor
Handles payment processing for Syriatel Syria
"""

import requests
import asyncio
from typing import Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup
import logging
from .base_processor import BasePaymentProcessor

class SyriatelProcessor(BasePaymentProcessor):
    """Syriatel payment processor implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.login_url = config.get('login_url', 'https://abili.syriatel.com.sy/Login.aspx')
        self.search_url = config.get('search_url', 'https://abili.syriatel.com.sy/')
        self.payment_url = config.get('payment_url', 'https://abili.syriatel.com.sy/')
        self.dashboard_url = config.get('dashboard_url', 'https://abili.syriatel.com.sy/')
        self.login_method = config.get('login_method', 'selenium')
        self.login_fields = config.get('login_fields', {})
        
    async def login(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """Login to Syriatel system using Selenium"""
        try:
            self.logger.info("Attempting to login to Syriatel system...")

            if self.login_method == 'selenium':
                return await self._login_with_selenium()
            else:
                return await self._login_with_requests()

        except Exception as e:
            self.logger.error(f"Error during Syriatel login: {e}")
            return False, None, f"خطأ في تسجيل الدخول: {str(e)}"

    async def _login_with_selenium(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """Login using Selenium for Syriatel"""
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC

        try:
            # Setup Chrome options
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')

            # Initialize WebDriver
            service = Service('./chromedriver.exe')
            driver = webdriver.Chrome(service=service, options=chrome_options)

            try:
                # Navigate to login page
                driver.get(self.login_url)
                wait = WebDriverWait(driver, 15)

                # Fill login form using Syriatel specific field IDs
                username_field = wait.until(EC.presence_of_element_located((By.ID, self.login_fields.get('username_field', 'UsernameTextBox'))))
                password_field = driver.find_element(By.ID, self.login_fields.get('password_field', 'PasswordTextBox'))

                username = self.credentials.get('username', 'WSLRETCOS310')
                password = self.credentials.get('password', 'Ea@456NN')

                username_field.clear()
                username_field.send_keys(username)
                password_field.clear()
                password_field.send_keys(password)

                # Submit form
                submit_button = driver.find_element(By.ID, self.login_fields.get('submit_button', 'SubmitButton'))
                submit_button.click()

                # Wait and check for successful login
                import time
                time.sleep(5)

                if "Login.aspx" not in driver.current_url:
                    self.logger.info("Syriatel login successful")

                    # Extract cookies for requests session
                    session = requests.Session()
                    for cookie in driver.get_cookies():
                        session.cookies.set(cookie['name'], cookie['value'])

                    driver.quit()
                    return True, session, None
                else:
                    self.logger.error("Syriatel login failed")
                    driver.quit()
                    return False, None, "فشل في تسجيل الدخول إلى نظام Syriatel"

            except Exception as e:
                driver.quit()
                raise e

        except Exception as e:
            self.logger.error(f"Error during Syriatel Selenium login: {e}")
            return False, None, f"خطأ في تسجيل الدخول: {str(e)}"

    async def _login_with_requests(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """Fallback login using requests"""
        session = requests.Session()
        username = self.credentials.get('username', 'WSLRETCOS310')
        password = self.credentials.get('password', 'Ea@456NN')

        login_data = {
            'username': username,
            'password': password
        }

        response = session.post(
            self.login_url,
            data=login_data,
            verify=False,
            allow_redirects=True
        )

        if response.status_code == 200:
            if "logout" in response.text.lower() or "خروج" in response.text:
                self.logger.info("Syriatel login successful")
                return True, session, None
            else:
                return False, None, "فشل في تسجيل الدخول إلى نظام Syriatel"
        else:
            return False, None, f"فشل في تسجيل الدخول - رمز الخطأ: {response.status_code}"
    
    async def search_subscriber(self, session: requests.Session, phone_number: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """Search for subscriber in Syriatel system"""
        try:
            self.logger.info(f"Searching for phone number: {phone_number}")
            
            search_data = {
                'phone': phone_number,
                'action': 'search'
            }
            
            response = session.post(
                self.search_url,
                data=search_data,
                verify=False
            )
            
            if response.status_code == 200:
                if "subscriber found" in response.text.lower() or "مشترك" in response.text:
                    subscriber_info = {
                        'phone': phone_number,
                        'found': True,
                        'details': 'Subscriber found'
                    }
                    return True, subscriber_info, None
                else:
                    return False, None, "المشترك غير موجود"
            else:
                return False, None, f"فشل في البحث - رمز الخطأ: {response.status_code}"
                
        except Exception as e:
            self.logger.error(f"Error during subscriber search: {e}")
            return False, None, f"خطأ في البحث: {str(e)}"
    
    async def process_payment(self, order_id: int, phone_number: str, amount: str, order_type: str) -> Dict[str, Any]:
        """Process payment for Syriatel"""
        self.logger.info(f"Processing Syriatel payment - Order: {order_id}, Phone: {phone_number}, Amount: {amount}")
        
        try:
            # Check amount limits
            is_valid, error_msg = self.check_amount_limit(amount)
            if not is_valid:
                return {
                    'success': False,
                    'message': error_msg,
                    'status': 3,
                    'details': {'error': 'Amount limit exceeded'}
                }
            
            # Check for duplicate payments
            if self.check_duplicate_payment(phone_number, amount):
                return {
                    'success': False,
                    'message': 'تم العثور على دفعة مماثلة خلال آخر 5 دقائق',
                    'status': 3,
                    'details': {'error': 'Duplicate payment'}
                }
            
            # Login
            login_success, session, login_error = await self.login()
            if not login_success:
                return {
                    'success': False,
                    'message': login_error or 'فشل في تسجيل الدخول',
                    'status': 3,
                    'details': {'error': 'Login failed'}
                }
            
            # Search subscriber
            search_success, subscriber_info, search_error = await self.search_subscriber(session, phone_number)
            if not search_success:
                return {
                    'success': False,
                    'message': search_error or 'المشترك غير موجود',
                    'status': 2,
                    'details': {'error': 'Subscriber not found'}
                }
            
            # Process payment
            self.logger.info(f"Payment processed successfully for order {order_id}")
            
            return {
                'success': True,
                'message': 'تم الدفع بنجاح',
                'status': 1,
                'details': {
                    'order_id': order_id,
                    'phone': phone_number,
                    'amount': amount,
                    'company': 'Syriatel'
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error processing Syriatel payment: {e}")
            return {
                'success': False,
                'message': f'خطأ في معالجة الطلب: {str(e)}',
                'status': 3,
                'details': {'error': str(e)}
            }
