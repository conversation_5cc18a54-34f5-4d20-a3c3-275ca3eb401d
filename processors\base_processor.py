#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Base Payment Processor Class
Provides common functionality for all payment processors
"""

import requests
import time
import logging
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from fake_useragent import UserAgent
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class BasePaymentProcessor(ABC):
    """Base class for all payment processors"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the payment processor
        Args:
            config: Configuration dictionary for the company
        """
        self.config = config
        self.company_name = config.get('name', 'Unknown')
        self.product_id = config.get('product_id')
        self.credentials = config.get('credentials', {})
        self.limits = config.get('limits', {})
        self.phone_format = config.get('phone_format', {})
        
        # Setup logging
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # Setup session with user agent
        self.session = requests.Session()
        ua = UserAgent()
        self.session.headers.update({
            'User-Agent': ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def format_phone_number(self, phone_number: str) -> str:
        """
        Format phone number according to company requirements
        Args:
            phone_number: Original phone number
        Returns:
            str: Formatted phone number
        """
        prefix = self.phone_format.get('prefix', '')
        if prefix and not phone_number.startswith(prefix):
            return f"{prefix}{phone_number}"
        return phone_number
    
    def check_amount_limit(self, amount: str) -> Tuple[bool, str]:
        """
        Check if amount is within allowed limits
        Args:
            amount: Payment amount as string
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        try:
            amount_int = int(amount)
            max_amount = self.limits.get('max_amount', 100000)
            
            if amount_int > max_amount:
                return False, f"المبلغ {amount} يتجاوز الحد الأقصى المسموح {max_amount}"
            
            if amount_int <= 0:
                return False, "المبلغ يجب أن يكون أكبر من صفر"
                
            return True, ""
            
        except ValueError:
            return False, "المبلغ غير صحيح"
    
    def check_duplicate_payment(self, phone_number: str, amount: str) -> bool:
        """
        Check for duplicate payments within the specified time window
        Args:
            phone_number: Customer phone number
            amount: Payment amount
        Returns:
            bool: True if duplicate found, False otherwise
        """
        # This is a simplified implementation
        # In a real system, you would check against a database
        return False
    
    def random_sleep(self, min_sec: float = 0.5, max_sec: float = 1.5):
        """
        Sleep for a random duration
        Args:
            min_sec: Minimum sleep duration
            max_sec: Maximum sleep duration
        """
        import random
        sleep_time = random.uniform(min_sec, max_sec)
        time.sleep(sleep_time)
    
    @abstractmethod
    async def login(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """
        Login to the company's system
        Returns:
            Tuple[bool, Optional[Session], Optional[str]]: (success, session, error_message)
        """
        pass
    
    @abstractmethod
    async def search_subscriber(self, session: requests.Session, phone_number: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """
        Search for subscriber information
        Args:
            session: Authenticated session
            phone_number: Phone number to search
        Returns:
            Tuple[bool, Optional[Dict], Optional[str]]: (success, subscriber_info, error_message)
        """
        pass
    
    @abstractmethod
    async def process_payment(self, order_id: int, phone_number: str, amount: str, order_type: str) -> Dict[str, Any]:
        """
        Process payment for the given order
        Args:
            order_id: Order ID
            phone_number: Customer phone number
            amount: Payment amount
            order_type: Type of order
        Returns:
            Dict[str, Any]: Payment result with status and message
        """
        pass
