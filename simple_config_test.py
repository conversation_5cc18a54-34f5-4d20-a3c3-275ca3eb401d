#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def test_config():
    print("Testing configuration loading...")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("Config loaded successfully!")
        print(f"Companies found: {list(config['companies'].keys())}")
        
        # Test MTS config
        mts_config = config['companies']['mts']
        print(f"MTS Login URL: {mts_config['login_url']}")
        print(f"MTS Username: {mts_config['credentials']['username']}")
        print(f"MTS Max Amount: {mts_config['limits']['max_amount']}")
        
        print("All tests passed!")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_config()
