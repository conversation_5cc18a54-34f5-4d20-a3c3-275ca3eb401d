# تفاصيل ملف التكوين - Config Details

## ملخص التحديثات

تم إعادة كتابة ملف `config.json` بالمعلومات الصحيحة المستخرجة من الملفات الأصلية لجميع الشركات.

## معلومات الشركات المحدثة

### 1. MTS Syria (Product ID: 32)
```json
{
  "login_url": "https://pos.ispcare.mts.sy/",
  "credentials": {
    "username": "alfager.net",
    "password": "Fager@123"
  },
  "phone_format": {
    "prefix": "41",
    "required": true
  },
  "duplicate_check_minutes": 5
}
```

### 2. Sawa Syria (Product ID: 29)
```json
{
  "login_url": "http://sp.sawaisp.sy/charge_balance_to_customer",
  "credentials": {
    "username": "fireware",
    "password": "idhmhgauvhx"
  },
  "login_method": "selenium",
  "duplicate_check_minutes": 1440
}
```

### 3. Syriatel Syria (Product ID: 25)
```json
{
  "login_url": "https://abili.syriatel.com.sy/Login.aspx",
  "credentials": {
    "username": "WSLRETCOS310",
    "password": "Ea@456NN"
  },
  "login_method": "selenium",
  "login_fields": {
    "username_field": "UsernameTextBox",
    "password_field": "PasswordTextBox",
    "submit_button": "SubmitButton"
  }
}
```

### 4. INET Syria (Product ID: 47)
```json
{
  "login_url": "https://pos.ispcare.inet.sy/",
  "credentials": {
    "username": "nawraspos",
    "password": "Idhm*0hgauvhx"
  }
}
```

### 5. Linet Syria (Product ID: 46)
```json
{
  "login_url": "https://pos.ispcare.linet-sy.com/",
  "credentials": {
    "username": "nawras-tel",
    "password": "Nn@456nn"
  },
  "login_data": {
    "lang": "ar"
  }
}
```

### 6. Takamol Syria (Product ID: 45)
```json
{
  "login_url": "https://pos.ispcare.takamol.sy/",
  "credentials": {
    "username": "firewire",
    "password": "Idhm*0hgauvhx"
  },
  "login_data": {
    "lang": "ar"
  }
}
```

### 7. LEMA Syria (Product ID: 148)
```json
{
  "login_url": "https://pos.ispcare.lema.sy/",
  "credentials": {
    "username": "ahmad-s",
    "password": "Meme.meme07"
  }
}
```

### 8. Bitakat Syria (Product ID: 50)
```json
{
  "login_url": "https://pos.ispcare.bitakat.sy/",
  "credentials": {
    "username": "naoras-tech",
    "password": "Idhm*9hgauvhx"
  }
}
```

## التحديثات المهمة

### ✅ تم إزالة timeout
- لا توجد إعدادات timeout في أي مكان
- تم إزالة جميع المهلات الزمنية

### ✅ URLs صحيحة
- جميع الروابط مستخرجة من الملفات الأصلية
- تم التحقق من صحة كل رابط

### ✅ بيانات تسجيل الدخول صحيحة
- جميع أسماء المستخدمين وكلمات المرور مستخرجة من الملفات الأصلية
- تم التحقق من صحة كل بيانات تسجيل دخول

### ✅ إعدادات خاصة لكل شركة
- **MTS**: يتطلب بادئة "41" لأرقام الهاتف
- **Sawa**: فترة تكرار 24 ساعة (1440 دقيقة)
- **Syriatel & Sawa**: يستخدمان Selenium للتسجيل
- **Linet & Takamol**: يتطلبان معامل "lang": "ar"

### ✅ API خارجي
```json
{
  "external_api": {
    "orders_url": "https://menfax.com/pos/public/api/orders",
    "status_url": "https://menfax.com/pos/public/api/change-order-status",
    "secret": "SECRET1265AQREFGHKLFS!@#"
  }
}
```

## اختبار التكوين

يمكنك اختبار التكوين باستخدام:

```bash
python simple_config_test.py
```

هذا سيتحقق من:
- تحميل ملف التكوين بنجاح
- وجود جميع الشركات
- صحة البيانات الأساسية

## ملاحظات الأمان

⚠️ **تحذير**: ملف التكوين يحتوي على بيانات تسجيل دخول حقيقية. تأكد من:
- عدم مشاركة هذا الملف علناً
- استخدام متغيرات البيئة في الإنتاج
- تشفير الملف إذا لزم الأمر
