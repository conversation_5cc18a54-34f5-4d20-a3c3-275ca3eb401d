#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI Payment Processor for Internet Companies
Processes payment requests for various Syrian internet service providers
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, Dict, Any
import json
import logging
from datetime import datetime
import asyncio
import os
import importlib.util

# Import payment processors
from processors.mts_processor import MTSProcessor
from processors.sawa_processor import SawaProcessor
from processors.syriatel_processor import SyriatelProcessor
from processors.inet_processor import INETProcessor
from processors.linet_processor import LinetProcessor
from processors.takamol_processor import TakamolProcessor
from processors.lema_processor import LemaProcessor
from processors.bitakat_processor import BitakatProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_main.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Internet Payment Processor API",
    description="API for processing payments to Syrian internet service providers",
    version="1.0.0"
)

# Load configuration
def load_config():
    """Load configuration from config.json"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        raise HTTPException(status_code=500, detail="Configuration error")

config = load_config()

# Request model
class PaymentRequest(BaseModel):
    id: int
    amount: str
    number: str
    code: Optional[str] = None
    product: int
    type: str
    date: str

# Response model
class PaymentResponse(BaseModel):
    success: bool
    message: str
    order_id: int
    status: int  # 1=paid, 2=number not found, 3=payment failed, 4=subscriber has debt
    details: Optional[Dict[str, Any]] = None

# Initialize processors
processors = {
    32: MTSProcessor(config['companies']['mts']),           # MTS
    29: SawaProcessor(config['companies']['sawa']),         # Sawa
    25: SyriatelProcessor(config['companies']['syriatel']), # Syriatel
    47: INETProcessor(config['companies']['inet']),         # INET
    46: LinetProcessor(config['companies']['linet']),       # Linet
    45: TakamolProcessor(config['companies']['takamol']),   # Takamol
    148: LemaProcessor(config['companies']['lema']),        # LEMA
    50: BitakatProcessor(config['companies']['bitakat'])    # Bitakat
}

def get_company_name_by_product_id(product_id: int) -> str:
    """Get company name by product ID"""
    company_map = {
        32: "mts",
        29: "sawa", 
        25: "syriatel",
        47: "inet",
        46: "linet",
        45: "takamol",
        148: "lema",
        50: "bitakat"
    }
    return company_map.get(product_id, "unknown")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Internet Payment Processor API",
        "version": "1.0.0",
        "supported_companies": list(config['companies'].keys()),
        "endpoints": {
            "process_payment": "/process-payment",
            "health": "/health",
            "companies": "/companies"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "processors_loaded": len(processors)
    }

@app.get("/companies")
async def get_companies():
    """Get list of supported companies and their product IDs"""
    companies_info = {}
    for company_key, company_config in config['companies'].items():
        companies_info[company_key] = {
            "name": company_config['name'],
            "product_id": company_config['product_id'],
            "max_amount": company_config['limits']['max_amount']
        }
    return companies_info

@app.post("/process-payment", response_model=PaymentResponse)
async def process_payment(request: PaymentRequest, background_tasks: BackgroundTasks):
    """
    Process a payment request for the specified company
    """
    logger.info(f"Received payment request: Order ID {request.id}, Product {request.product}, Amount {request.amount}, Number {request.number}")
    
    # Validate product ID
    if request.product not in processors:
        company_name = get_company_name_by_product_id(request.product)
        logger.error(f"Unsupported product ID: {request.product} (Company: {company_name})")
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported company product ID: {request.product}. Company: {company_name}"
        )
    
    # Get the appropriate processor
    processor = processors[request.product]
    company_name = get_company_name_by_product_id(request.product)
    
    logger.info(f"Processing payment for {company_name.upper()} - Order ID: {request.id}")
    
    try:
        # Process the payment
        result = await processor.process_payment(
            order_id=request.id,
            phone_number=request.number,
            amount=request.amount,
            order_type=request.type
        )
        
        logger.info(f"Payment processing completed for Order ID {request.id}: Success={result['success']}, Status={result['status']}")
        
        return PaymentResponse(
            success=result['success'],
            message=result['message'],
            order_id=request.id,
            status=result['status'],
            details=result.get('details')
        )
        
    except Exception as e:
        logger.error(f"Error processing payment for Order ID {request.id}: {str(e)}")
        return PaymentResponse(
            success=False,
            message=f"خطأ في معالجة الطلب: {str(e)}",
            order_id=request.id,
            status=3,  # Payment failed
            details={"error": str(e)}
        )

@app.post("/process-payment-async")
async def process_payment_async(request: PaymentRequest, background_tasks: BackgroundTasks):
    """
    Process a payment request asynchronously (fire and forget)
    Returns immediately with acceptance confirmation
    """
    logger.info(f"Received async payment request: Order ID {request.id}, Product {request.product}")
    
    # Validate product ID
    if request.product not in processors:
        company_name = get_company_name_by_product_id(request.product)
        logger.error(f"Unsupported product ID: {request.product} (Company: {company_name})")
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported company product ID: {request.product}. Company: {company_name}"
        )
    
    # Add payment processing to background tasks
    background_tasks.add_task(process_payment_background, request)
    
    return {
        "message": "Payment request accepted and will be processed",
        "order_id": request.id,
        "status": "accepted"
    }

async def process_payment_background(request: PaymentRequest):
    """Background task for processing payments"""
    processor = processors[request.product]
    company_name = get_company_name_by_product_id(request.product)
    
    logger.info(f"Background processing payment for {company_name.upper()} - Order ID: {request.id}")
    
    try:
        result = await processor.process_payment(
            order_id=request.id,
            phone_number=request.number,
            amount=request.amount,
            order_type=request.type
        )
        
        logger.info(f"Background payment processing completed for Order ID {request.id}: Success={result['success']}, Status={result['status']}")
        
    except Exception as e:
        logger.error(f"Error in background payment processing for Order ID {request.id}: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    
    # Create processors directory if it doesn't exist
    os.makedirs("processors", exist_ok=True)
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    logger.info("Starting Internet Payment Processor API...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )