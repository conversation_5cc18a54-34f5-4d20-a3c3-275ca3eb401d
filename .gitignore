# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log
*_log.txt
*_tracking.txt
*_processed_orders.txt

# Config files with sensitive data
config_production.json

# OS
.DS_Store
Thumbs.db

# Selenium
chromedriver.exe
geckodriver.exe

# Temporary files
*.tmp
*.temp

# Database
*.db
*.sqlite3

# Environment variables
.env
.env.local
.env.production

# Docker
.dockerignore
