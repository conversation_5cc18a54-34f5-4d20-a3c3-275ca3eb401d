#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test to verify the API works
"""

from main import app
from fastapi.testclient import TestClient

def test_api():
    """Test the API endpoints"""
    client = TestClient(app)
    
    # Test root endpoint
    print("Testing root endpoint...")
    response = client.get("/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)
    
    # Test health endpoint
    print("Testing health endpoint...")
    response = client.get("/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)
    
    # Test companies endpoint
    print("Testing companies endpoint...")
    response = client.get("/companies")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)
    
    # Test payment processing
    print("Testing payment processing...")
    test_payment = {
        "id": 585241,
        "amount": "24000",
        "number": "7271141",
        "code": None,
        "product": 32,  # MTS
        "type": "2 MB",
        "date": "2025-08-06T13:00:25.000000Z"
    }
    
    response = client.post("/process-payment", json=test_payment)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

if __name__ == "__main__":
    print("Starting API tests...")
    try:
        test_api()
        print("All tests completed successfully!")
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
