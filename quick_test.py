#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test for the updated config and processors
"""

import json
import asyncio
from processors.mts_processor import MTSProcessor

async def test_config_and_processors():
    """Test the updated configuration and processors"""
    
    print("🔧 Testing configuration loading...")
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ Configuration loaded successfully")
        print(f"📋 Companies found: {list(config['companies'].keys())}")
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return
    
    print("\n🏢 Testing MTS processor...")
    try:
        mts_config = config['companies']['mts']
        processor = MTSProcessor(mts_config)
        print("✅ MTS processor created successfully")
        print(f"📍 Login URL: {processor.login_url}")
        print(f"👤 Username: {processor.credentials['username']}")
        print(f"📞 Phone prefix: {processor.phone_format['prefix']}")
        print(f"💰 Max amount: {processor.limits['max_amount']}")
        print(f"⏰ Duplicate check: {processor.limits['duplicate_check_minutes']} minutes")
    except Exception as e:
        print(f"❌ Error creating MTS processor: {e}")
        return
    
    print("\n🧪 Testing phone number formatting...")
    try:
        test_number = "7271141"
        formatted = processor.format_phone_number(test_number)
        print(f"📞 Original: {test_number}")
        print(f"📞 Formatted: {formatted}")
        print("✅ Phone formatting works")
    except Exception as e:
        print(f"❌ Error in phone formatting: {e}")
    
    print("\n💰 Testing amount validation...")
    try:
        test_amounts = ["24000", "150000", "0", "abc"]
        for amount in test_amounts:
            is_valid, error = processor.check_amount_limit(amount)
            status = "✅" if is_valid else "❌"
            print(f"{status} Amount {amount}: {'Valid' if is_valid else error}")
    except Exception as e:
        print(f"❌ Error in amount validation: {e}")
    
    print("\n🎯 All tests completed!")

if __name__ == "__main__":
    asyncio.run(test_config_and_processors())
