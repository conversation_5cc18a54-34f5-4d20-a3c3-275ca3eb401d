#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sawa Payment Processor
Handles payment processing for Sawa Syria
"""

import requests
import asyncio
from typing import Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from .base_processor import BasePaymentProcessor

class SawaProcessor(BasePaymentProcessor):
    """Sawa payment processor implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.login_url = config.get('login_url', 'http://sp.sawaisp.sy/charge_balance_to_customer')
        self.search_url = config.get('search_url', 'http://sp.sawaisp.sy/')
        self.payment_url = config.get('payment_url', 'http://sp.sawaisp.sy/')
        self.dashboard_url = config.get('dashboard_url', 'http://sp.sawaisp.sy/')
        self.login_method = config.get('login_method', 'selenium')
        
    async def login(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """
        Login to Sawa system using Selenium
        Returns:
            Tuple[bool, Optional[Session], Optional[str]]: (success, session, error_message)
        """
        try:
            self.logger.info("Attempting to login to Sawa system...")
            
            # Setup Chrome options
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # Initialize WebDriver
            service = Service('./chromedriver.exe')
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            try:
                # Navigate to login page
                driver.get(self.login_url)
                
                # Wait for login form
                wait = WebDriverWait(driver, 10)
                
                # Find and fill login form (Sawa specific)
                username_field = wait.until(EC.presence_of_element_located((By.NAME, "username")))
                password_field = driver.find_element(By.NAME, "password")

                username = self.credentials.get('username', 'fireware')
                password = self.credentials.get('password', 'idhmhgauvhx')

                username_field.send_keys(username)
                password_field.send_keys(password)

                # Submit form using Sawa specific button
                login_button = driver.find_element(By.XPATH, "/html/body/section/div/div[1]/div[2]/form/input[5]")
                login_button.click()
                
                # Wait for Sawa specific success indicator
                wait.until(EC.presence_of_element_located((By.ID, "select2-chosen-1")))

                # Check if login was successful (Sawa specific)
                if driver.current_url != self.login_url:
                    self.logger.info("Sawa login successful")
                    
                    # Extract cookies for requests session
                    session = requests.Session()
                    for cookie in driver.get_cookies():
                        session.cookies.set(cookie['name'], cookie['value'])
                    
                    driver.quit()
                    return True, session, None
                else:
                    self.logger.error("Sawa login failed")
                    driver.quit()
                    return False, None, "فشل في تسجيل الدخول إلى نظام Sawa"
                    
            except Exception as e:
                driver.quit()
                raise e
                
        except Exception as e:
            self.logger.error(f"Error during Sawa login: {e}")
            return False, None, f"خطأ في تسجيل الدخول: {str(e)}"
    
    async def search_subscriber(self, session: requests.Session, phone_number: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """
        Search for subscriber in Sawa system
        Args:
            session: Authenticated session
            phone_number: Phone number to search
        Returns:
            Tuple[bool, Optional[Dict], Optional[str]]: (success, subscriber_info, error_message)
        """
        try:
            self.logger.info(f"Searching for phone number: {phone_number}")
            
            # Search request data
            search_data = {
                'phone': phone_number,
                'action': 'search'
            }
            
            # Send search request
            response = session.post(
                self.search_url,
                data=search_data,
                verify=False
            )
            
            if response.status_code == 200:
                # Parse response to extract subscriber info
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for subscriber information in the response
                if "subscriber found" in response.text.lower() or "مشترك" in response.text:
                    subscriber_info = {
                        'phone': phone_number,
                        'found': True,
                        'details': 'Subscriber found'
                    }
                    self.logger.info(f"Subscriber found for phone: {phone_number}")
                    return True, subscriber_info, None
                else:
                    self.logger.warning(f"Subscriber not found for phone: {phone_number}")
                    return False, None, "المشترك غير موجود"
            else:
                self.logger.error(f"Search request failed with status: {response.status_code}")
                return False, None, f"فشل في البحث - رمز الخطأ: {response.status_code}"
                
        except Exception as e:
            self.logger.error(f"Error during subscriber search: {e}")
            return False, None, f"خطأ في البحث: {str(e)}"
    
    async def process_payment(self, order_id: int, phone_number: str, amount: str, order_type: str) -> Dict[str, Any]:
        """
        Process payment for Sawa
        Args:
            order_id: Order ID
            phone_number: Customer phone number
            amount: Payment amount
            order_type: Type of order
        Returns:
            Dict[str, Any]: Payment result
        """
        self.logger.info(f"Processing Sawa payment - Order: {order_id}, Phone: {phone_number}, Amount: {amount}")
        
        try:
            # Check amount limits
            is_valid, error_msg = self.check_amount_limit(amount)
            if not is_valid:
                return {
                    'success': False,
                    'message': error_msg,
                    'status': 3,  # Payment failed
                    'details': {'error': 'Amount limit exceeded'}
                }
            
            # Check for duplicate payments
            if self.check_duplicate_payment(phone_number, amount):
                return {
                    'success': False,
                    'message': 'تم العثور على دفعة مماثلة خلال آخر 24 ساعة',
                    'status': 3,  # Payment failed
                    'details': {'error': 'Duplicate payment'}
                }
            
            # Step 1: Login
            login_success, session, login_error = await self.login()
            if not login_success:
                return {
                    'success': False,
                    'message': login_error or 'فشل في تسجيل الدخول',
                    'status': 3,  # Payment failed
                    'details': {'error': 'Login failed'}
                }
            
            # Step 2: Search subscriber
            search_success, subscriber_info, search_error = await self.search_subscriber(session, phone_number)
            if not search_success:
                return {
                    'success': False,
                    'message': search_error or 'المشترك غير موجود',
                    'status': 2,  # Number not found
                    'details': {'error': 'Subscriber not found'}
                }
            
            # Step 3: Process payment (simplified implementation)
            self.logger.info(f"Payment processed successfully for order {order_id}")
            
            return {
                'success': True,
                'message': 'تم الدفع بنجاح',
                'status': 1,  # Paid successfully
                'details': {
                    'order_id': order_id,
                    'phone': phone_number,
                    'amount': amount,
                    'company': 'Sawa'
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error processing Sawa payment: {e}")
            return {
                'success': False,
                'message': f'خطأ في معالجة الطلب: {str(e)}',
                'status': 3,  # Payment failed
                'details': {'error': str(e)}
            }
